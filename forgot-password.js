// ملف JavaScript لصفحة استعادة كلمة المرور
// Forgot Password JavaScript File

document.addEventListener('DOMContentLoaded', function() {
    initializeForgotPassword();
});

function initializeForgotPassword() {
    const resetMethods = document.querySelectorAll('.reset-method');
    const resetForms = document.querySelectorAll('.reset-form');
    
    // إضافة مستمعي الأحداث لطرق الاستعادة
    resetMethods.forEach(method => {
        method.addEventListener('click', function() {
            const methodType = this.dataset.method;
            selectResetMethod(methodType);
        });
    });
    
    // إضافة مستمعي الأحداث للنماذج
    setupFormHandlers();
    
    // تأثيرات بصرية
    addVisualEffects();
}

function selectResetMethod(methodType) {
    // إزالة التحديد من جميع الطرق
    document.querySelectorAll('.reset-method').forEach(method => {
        method.classList.remove('active');
    });
    
    // إخفاء جميع النماذج
    document.querySelectorAll('.reset-form').forEach(form => {
        form.classList.remove('active');
    });
    
    // تحديد الطريقة المختارة
    document.querySelector(`[data-method="${methodType}"]`).classList.add('active');
    
    // إظهار النموذج المناسب
    const targetForm = document.getElementById(`${methodType}Form`);
    if (targetForm) {
        setTimeout(() => {
            targetForm.classList.add('active');
            targetForm.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 200);
    }
    
    // تسجيل الحدث
    console.log(`تم اختيار طريقة الاستعادة: ${methodType}`);
}

function setupFormHandlers() {
    // نموذج البريد الإلكتروني
    const emailForm = document.getElementById('emailResetForm');
    if (emailForm) {
        emailForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleEmailReset();
        });
    }
    
    // نموذج الهاتف
    const phoneForm = document.getElementById('phoneResetForm');
    if (phoneForm) {
        phoneForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handlePhoneReset();
        });
    }
    
    // نموذج الأسئلة الأمنية
    const securityForm = document.getElementById('securityResetForm');
    if (securityForm) {
        securityForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSecurityReset();
        });
    }
}

function handleEmailReset() {
    const email = document.getElementById('email').value;
    
    if (!validateEmail(email)) {
        showError('يرجى إدخال بريد إلكتروني صحيح');
        return;
    }
    
    // محاكاة إرسال البريد الإلكتروني
    showLoading('emailResetForm');
    
    setTimeout(() => {
        hideLoading('emailResetForm');
        
        // التحقق من البريد الإلكتروني المسجل
        const registeredEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
        
        if (registeredEmails.includes(email.toLowerCase())) {
            showSuccessMessage();
            // محاكاة إرسال رمز الاستعادة
            generateResetCode(email, 'email');

            // إعادة التوجيه لصفحة إدخال الرمز
            setTimeout(() => {
                window.location.href = 'reset-code.html';
            }, 3000);
        } else {
            showError('البريد الإلكتروني غير مسجل في النظام');
        }
    }, 2000);
}

function handlePhoneReset() {
    const phone = document.getElementById('phone').value;
    
    if (!validatePhone(phone)) {
        showError('يرجى إدخال رقم هاتف صحيح');
        return;
    }
    
    // محاكاة إرسال الرسالة النصية
    showLoading('phoneResetForm');
    
    setTimeout(() => {
        hideLoading('phoneResetForm');
        
        // التحقق من رقم الهاتف المسجل
        const registeredPhones = [
            '+966501234567',
            '+966551234567',
            '0501234567',
            '0551234567'
        ];
        
        if (registeredPhones.includes(phone)) {
            showSuccessMessage();
            // محاكاة إرسال رمز الاستعادة
            generateResetCode(phone, 'phone');

            // إعادة التوجيه لصفحة إدخال الرمز
            setTimeout(() => {
                window.location.href = 'reset-code.html';
            }, 3000);
        } else {
            showError('رقم الهاتف غير مسجل في النظام');
        }
    }, 2000);
}

function handleSecurityReset() {
    const question1 = document.querySelector('[name="question1"]').value;
    const question2 = document.querySelector('[name="question2"]').value;
    
    if (!question1 || !question2) {
        showError('يرجى الإجابة على جميع الأسئلة');
        return;
    }
    
    // محاكاة التحقق من الأسئلة الأمنية
    showLoading('securityResetForm');
    
    setTimeout(() => {
        hideLoading('securityResetForm');
        
        // إجابات صحيحة للاختبار
        const correctAnswers = {
            question1: ['الرياض', 'جدة', 'الدمام', 'مكة'],
            question2: ['قطة', 'كلب', 'عصفور', 'سمك']
        };
        
        const answer1Correct = correctAnswers.question1.some(answer => 
            answer.toLowerCase().includes(question1.toLowerCase()) || 
            question1.toLowerCase().includes(answer.toLowerCase())
        );
        
        const answer2Correct = correctAnswers.question2.some(answer => 
            answer.toLowerCase().includes(question2.toLowerCase()) || 
            question2.toLowerCase().includes(answer.toLowerCase())
        );
        
        if (answer1Correct && answer2Correct) {
            showPasswordResetForm();
        } else {
            showError('الإجابات غير صحيحة. يرجى المحاولة مرة أخرى.');
        }
    }, 2000);
}

function generateResetCode(contact, method) {
    // إنشاء رمز استعادة عشوائي
    const resetCode = Math.floor(100000 + Math.random() * 900000);
    
    // حفظ الرمز في localStorage للاختبار
    const resetData = {
        code: resetCode,
        contact: contact,
        method: method,
        timestamp: Date.now(),
        expires: Date.now() + (15 * 60 * 1000) // ينتهي خلال 15 دقيقة
    };
    
    localStorage.setItem('resetCode', JSON.stringify(resetData));
    
    // عرض الرمز في console للاختبار
    console.log(`رمز الاستعادة: ${resetCode}`);
    console.log(`صالح لمدة 15 دقيقة`);
    
    // في التطبيق الحقيقي، سيتم إرسال الرمز عبر البريد الإلكتروني أو SMS
    if (method === 'email') {
        console.log(`تم إرسال الرمز إلى: ${contact}`);
    } else if (method === 'phone') {
        console.log(`تم إرسال الرمز إلى: ${contact}`);
    }
}

function showPasswordResetForm() {
    // إخفاء النماذج الحالية
    document.querySelectorAll('.reset-form').forEach(form => {
        form.classList.remove('active');
    });
    
    // إنشاء نموذج إعادة تعيين كلمة المرور
    const resetContainer = document.querySelector('.reset-container');
    const newPasswordForm = document.createElement('div');
    newPasswordForm.className = 'reset-form active';
    newPasswordForm.innerHTML = `
        <h3><i class="fas fa-lock"></i> تعيين كلمة مرور جديدة</h3>
        <form id="newPasswordForm">
            <div class="form-group">
                <label for="newPassword">كلمة المرور الجديدة:</label>
                <input type="password" id="newPassword" name="newPassword" required 
                       placeholder="أدخل كلمة المرور الجديدة" minlength="8">
            </div>
            <div class="form-group">
                <label for="confirmPassword">تأكيد كلمة المرور:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required 
                       placeholder="أعد إدخال كلمة المرور">
            </div>
            <button type="submit" class="login-btn">
                <span class="btn-text">
                    <i class="fas fa-save"></i>
                    حفظ كلمة المرور الجديدة
                </span>
            </button>
        </form>
    `;
    
    resetContainer.insertBefore(newPasswordForm, document.querySelector('.back-to-login'));
    
    // إضافة مستمع الحدث للنموذج الجديد
    document.getElementById('newPasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleNewPassword();
    });
}

function handleNewPassword() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (newPassword !== confirmPassword) {
        showError('كلمات المرور غير متطابقة');
        return;
    }
    
    if (newPassword.length < 8) {
        showError('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
        return;
    }
    
    // محاكاة حفظ كلمة المرور الجديدة
    showLoading('newPasswordForm');
    
    setTimeout(() => {
        hideLoading('newPasswordForm');
        
        // حفظ كلمة المرور الجديدة (في التطبيق الحقيقي)
        console.log('تم حفظ كلمة المرور الجديدة');
        
        // إظهار رسالة نجاح وإعادة توجيه
        showFinalSuccess();
    }, 2000);
}

function showFinalSuccess() {
    const resetContainer = document.querySelector('.reset-container');
    resetContainer.innerHTML = `
        <div style="text-align: center; padding: 3rem;">
            <i class="fas fa-check-circle" style="font-size: 4rem; color: #10b981; margin-bottom: 2rem;"></i>
            <h2 style="color: #10b981; margin-bottom: 1rem;">تم تغيير كلمة المرور بنجاح!</h2>
            <p style="color: #6b7280; margin-bottom: 2rem;">يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة</p>
            <a href="login.html" class="login-btn" style="display: inline-block; text-decoration: none;">
                <span class="btn-text">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </span>
            </a>
        </div>
    `;
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePhone(phone) {
    const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

function showLoading(formId) {
    const form = document.getElementById(formId);
    const button = form.querySelector('button[type="submit"]');
    const btnText = button.querySelector('.btn-text');
    
    btnText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
    button.disabled = true;
}

function hideLoading(formId) {
    const form = document.getElementById(formId);
    const button = form.querySelector('button[type="submit"]');
    const btnText = button.querySelector('.btn-text');
    
    // استعادة النص الأصلي
    const originalTexts = {
        'emailResetForm': '<i class="fas fa-paper-plane"></i> إرسال رمز الاستعادة',
        'phoneResetForm': '<i class="fas fa-sms"></i> إرسال رمز الاستعادة',
        'securityResetForm': '<i class="fas fa-check"></i> التحقق من الإجابات',
        'newPasswordForm': '<i class="fas fa-save"></i> حفظ كلمة المرور الجديدة'
    };
    
    btnText.innerHTML = originalTexts[formId] || '<i class="fas fa-check"></i> متابعة';
    button.disabled = false;
}

function showSuccessMessage() {
    const successMessage = document.getElementById('successMessage');
    successMessage.classList.add('show');
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        successMessage.classList.remove('show');
    }, 5000);
}

function showError(message) {
    // إنشاء رسالة خطأ
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        background: #ef4444;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        text-align: center;
        animation: fadeInUp 0.3s ease;
    `;
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    
    // إدراج الرسالة
    const activeForm = document.querySelector('.reset-form.active');
    if (activeForm) {
        activeForm.insertBefore(errorDiv, activeForm.firstChild);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
}

function addVisualEffects() {
    // تأثير hover للطرق
    const methods = document.querySelectorAll('.reset-method');
    methods.forEach(method => {
        method.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });
        
        method.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
    
    // تأثير focus للحقول
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
}
