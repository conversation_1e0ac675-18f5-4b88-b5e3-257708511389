// Login Script for Information Security Management System
class LoginManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupPasswordToggle();
        this.setupFormValidation();
        this.checkRememberedUser();
    }

    setupEventListeners() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Add enter key support for inputs
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleLogin(e);
                }
            });
        });

        // Add input animation effects
        inputs.forEach(input => {
            input.addEventListener('focus', () => this.addInputFocus(input));
            input.addEventListener('blur', () => this.removeInputFocus(input));
        });
    }

    setupPasswordToggle() {
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordInput = document.getElementById('password');

        if (passwordToggle && passwordInput) {
            passwordToggle.addEventListener('click', () => {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = passwordToggle.querySelector('i');
                if (type === 'password') {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                } else {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                }
            });
        }
    }

    setupFormValidation() {
        const inputs = document.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateInput(input));
            input.addEventListener('input', () => this.clearInputError(input));
        });
    }

    validateInput(input) {
        const value = input.value.trim();
        const isValid = value.length > 0;

        if (!isValid) {
            this.showInputError(input, 'هذا الحقل مطلوب');
        } else {
            this.clearInputError(input);
        }

        return isValid;
    }

    showInputError(input, message) {
        input.style.borderColor = 'var(--danger)';
        input.style.backgroundColor = 'rgba(220, 38, 38, 0.05)';
        
        // Remove existing error message
        const existingError = input.parentNode.querySelector('.input-error');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'input-error';
        errorDiv.style.cssText = `
            color: var(--danger);
            font-size: 0.8rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        `;
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
        input.parentNode.appendChild(errorDiv);
    }

    clearInputError(input) {
        input.style.borderColor = '';
        input.style.backgroundColor = '';
        
        const errorDiv = input.parentNode.querySelector('.input-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    addInputFocus(input) {
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            formGroup.style.transform = 'scale(1.02)';
        }
    }

    removeInputFocus(input) {
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            formGroup.style.transform = '';
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();
        const rememberMe = document.getElementById('rememberMe').checked;

        // Validate inputs
        const usernameValid = this.validateInput(document.getElementById('username'));
        const passwordValid = this.validateInput(document.getElementById('password'));

        if (!usernameValid || !passwordValid) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // Show loading state
        this.setLoadingState(true);
        this.hideError();

        try {
            // Simulate API call delay
            await this.delay(1500);

            // تحقق من المستخدم في systemUsers
            const users = JSON.parse(localStorage.getItem('systemUsers')) || {};
            let user = users[username];
            // دعم تسجيل الدخول باسم المستخدم أو البريد الإلكتروني
            if (!user) {
                user = Object.values(users).find(u => u.email === username);
            }
            let isAuthenticated = false;
            if (user && user.isActive) {
                // تحقق من كلمة المرور (مقارنة مع المخزن بعد التشفير)
                // ملاحظة: يجب أن يكون نفس خوارزمية hashPassword في auth.js
                function simpleHash(str) {
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                        hash = ((hash << 5) - hash) + str.charCodeAt(i);
                        hash |= 0;
                    }
                    return hash.toString();
                }
                const inputHash = simpleHash(password);
                if (user.password === inputHash) {
                    isAuthenticated = true;
                }
            }

            if (isAuthenticated) {
                // إنشاء session متوافقة مع النظام
                const sessionId = 'SID-' + Date.now() + '-' + Math.floor(Math.random()*10000);
                const expiresAt = new Date(Date.now() + 12*60*60*1000).toISOString(); // 12 ساعة
                const userSessions = JSON.parse(localStorage.getItem('userSessions')) || {};
                userSessions[sessionId] = {
                    username: user.username,
                    loginTime: new Date().toISOString(),
                    expiresAt: expiresAt
                };
                localStorage.setItem('userSessions', JSON.stringify(userSessions));
                localStorage.setItem('currentSession', sessionId);
                // تفعيل rememberMe إذا لزم
                if (rememberMe) {
                    localStorage.setItem('rememberedUser', user.username);
                }
                // Show success and redirect
                this.showSuccess('تم تسجيل الدخول بنجاح');
                await this.delay(1000);
                window.location.href = 'index.html';
            } else {
                this.showError('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
        } finally {
            this.setLoadingState(false);
        }
    }

    authenticateUser(username, password) {
        // Default credentials (in real app, this would be handled by backend)
        const validCredentials = [
            { username: 'admin', password: 'admin123' },
            { username: 'المدير', password: '123456' },
            { username: 'user', password: 'user123' },
            { username: 'مستخدم', password: '123456' }
        ];

        return validCredentials.some(cred => 
            cred.username === username && cred.password === password
        );
    }

    saveUserSession(username, rememberMe) {
        const userData = {
            username: username,
            loginTime: new Date().toISOString(),
            rememberMe: rememberMe
        };

        if (rememberMe) {
            localStorage.setItem('rememberedUser', username);
            localStorage.setItem('userSession', JSON.stringify(userData));
        } else {
            sessionStorage.setItem('userSession', JSON.stringify(userData));
        }
    }

    checkRememberedUser() {
        const rememberedUser = localStorage.getItem('rememberedUser');
        if (rememberedUser) {
            document.getElementById('username').value = rememberedUser;
            document.getElementById('rememberMe').checked = true;
        }
    }

    setLoadingState(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');

        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'flex';
            loginBtn.disabled = true;
        } else {
            btnText.style.display = 'flex';
            btnLoading.style.display = 'none';
            loginBtn.disabled = false;
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        if (errorDiv && errorText) {
            errorText.textContent = message;
            errorDiv.style.display = 'flex';
            errorDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    hideError() {
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    showSuccess(message) {
        // Create success message
        const successDiv = document.createElement('div');
        successDiv.style.cssText = `
            background: rgba(5, 150, 105, 0.1);
            border: 1px solid rgba(5, 150, 105, 0.3);
            color: var(--success);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            animation: slideInUp 0.3s ease;
        `;
        successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
        
        const form = document.getElementById('loginForm');
        form.appendChild(successDiv);
        
        // Remove after delay
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize login manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});

// Add some visual enhancements
document.addEventListener('DOMContentLoaded', () => {
    // Add stagger animation to form elements
    const formElements = document.querySelectorAll('.form-group, .form-options, .login-btn');
    formElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.animation = `fadeInUp 0.6s ease ${index * 0.1}s forwards`;
    });

    // Add hover effect to logo
    const logo = document.querySelector('.logo-container');
    if (logo) {
        logo.addEventListener('mouseenter', () => {
            logo.style.transform = 'scale(1.05) rotate(2deg)';
        });
        logo.addEventListener('mouseleave', () => {
            logo.style.transform = '';
        });
    }
});

// Add CSS animation keyframes
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
