// Simplified Authentication System
class SimpleAuthManager {
    constructor() {
        this.currentUser = null;
        this.initializeUsers();
        console.log('SimpleAuthManager initialized');
    }

    // Simple hash function
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Initialize default users
    initializeUsers() {
        this.users = {
            'admin': {
                id: 'admin',
                username: 'admin',
                password: this.hashPassword('admin123'),
                fullName: 'مدير النظام',
                email: '<EMAIL>',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null
            }
        };
        console.log('Users initialized:', Object.keys(this.users));
        console.log('Admin password hash:', this.users.admin.password);
    }

    // Authenticate user
    authenticateUser(username, password) {
        console.log('Authenticating user:', username);
        
        const user = this.users[username];
        if (!user) {
            console.log('User not found');
            return null;
        }

        if (!user.isActive) {
            console.log('User is inactive');
            return null;
        }

        const hashedPassword = this.hashPassword(password);
        console.log('Input password hash:', hashedPassword);
        console.log('Stored password hash:', user.password);
        console.log('Passwords match:', hashedPassword === user.password);

        if (user.password === hashedPassword) {
            console.log('Authentication successful');
            return user;
        }

        console.log('Authentication failed');
        return null;
    }

    // Handle login
    handleLogin() {
        console.log('handleLogin called');
        
        const usernameEl = document.getElementById('username');
        const passwordEl = document.getElementById('password');
        const rememberMeEl = document.getElementById('rememberMe');
        
        if (!usernameEl || !passwordEl) {
            console.error('Login form elements not found');
            alert('خطأ: عناصر النموذج غير موجودة');
            return;
        }

        const username = usernameEl.value.trim();
        const password = passwordEl.value;
        const rememberMe = rememberMeEl ? rememberMeEl.checked : false;

        console.log('Login attempt:', { username, rememberMe });

        if (!username || !password) {
            alert('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        // Show loading
        this.showLoading(true);

        // Authenticate
        const user = this.authenticateUser(username, password);

        if (user) {
            console.log('Login successful, redirecting...');
            this.loginSuccess(user, rememberMe);
        } else {
            console.log('Login failed');
            alert('اسم المستخدم أو كلمة المرور غير صحيح');
            this.showLoading(false);
        }
    }

    // Login success
    loginSuccess(user, rememberMe) {
        try {
            this.currentUser = user;

            // Update last login
            user.lastLogin = new Date().toISOString();

            // Create session
            const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
            const session = {
                userId: user.id,
                username: user.username,
                loginTime: new Date().toISOString(),
                rememberMe: rememberMe
            };

            // Save session
            localStorage.setItem('currentSession', sessionId);
            localStorage.setItem('userSession_' + sessionId, JSON.stringify(session));

            console.log('Session created:', sessionId);

            // Redirect to main application
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Error during login success:', error);
            alert('حدث خطأ أثناء تسجيل الدخول');
            this.showLoading(false);
        }
    }

    // Show loading state
    showLoading(show) {
        const loginBtn = document.getElementById('loginBtn');
        if (!loginBtn) return;

        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');

        if (show) {
            loginBtn.disabled = true;
            if (btnText) btnText.style.opacity = '0';
            if (btnLoading) btnLoading.style.display = 'block';
        } else {
            loginBtn.disabled = false;
            if (btnText) btnText.style.opacity = '1';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    }

    // Setup event listeners
    setupEventListeners() {
        console.log('Setting up event listeners');
        
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            console.log('Login form found, adding event listener');
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                console.log('Form submitted');
                this.handleLogin();
            });
        } else {
            console.log('Login form not found');
        }

        // Password toggle
        const passwordToggle = document.getElementById('passwordToggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                const passwordInput = document.getElementById('password');
                const icon = passwordToggle.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    passwordInput.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            });
        }

        // Enter key to submit
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
                const loginForm = document.getElementById('loginForm');
                if (loginForm && document.activeElement &&
                    (document.activeElement.id === 'username' || document.activeElement.id === 'password')) {
                    e.preventDefault();
                    this.handleLogin();
                }
            }
        });
    }

    // Check existing session
    checkExistingSession() {
        if (!window.location.pathname.includes('login.html')) {
            return false;
        }

        const sessionId = localStorage.getItem('currentSession');
        if (!sessionId) return false;

        const sessionData = localStorage.getItem('userSession_' + sessionId);
        if (!sessionData) return false;

        try {
            const session = JSON.parse(sessionData);
            console.log('Existing session found, redirecting to index.html');
            window.location.href = 'index.html';
            return true;
        } catch (error) {
            console.error('Error checking session:', error);
            return false;
        }
    }
}

// Create global instance
const simpleAuthManager = new SimpleAuthManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing simple auth manager');
    simpleAuthManager.setupEventListeners();
    simpleAuthManager.checkExistingSession();
    console.log('Simple auth manager initialized');
});

// Export for testing
if (typeof window !== 'undefined') {
    window.simpleAuthManager = simpleAuthManager;
}
