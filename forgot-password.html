<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة كلمة المرور - نظام إدارة أمن المعلومات</title>
    <link rel="stylesheet" href="login-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="manifest" href="manifest.json">
    <style>
        .reset-container {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .reset-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .reset-icon {
            font-size: 3rem;
            color: var(--primary-blue);
            margin-bottom: 1rem;
            display: block;
        }
        
        .reset-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .reset-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.5;
        }
        
        .reset-methods {
            display: grid;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .reset-method {
            background: white;
            border: 2px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .reset-method:hover {
            border-color: var(--primary-blue);
            background: rgba(59, 130, 246, 0.02);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
        }
        
        .reset-method.active {
            border-color: var(--primary-blue);
            background: rgba(59, 130, 246, 0.05);
        }
        
        .method-icon {
            font-size: 1.5rem;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }
        
        .method-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.3rem;
        }
        
        .method-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .reset-form {
            background: white;
            border: 2px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            display: none;
        }
        
        .reset-form.active {
            display: block;
            animation: fadeInUp 0.3s ease;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .success-message {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 2rem;
            display: none;
        }
        
        .success-message.show {
            display: block;
            animation: fadeInUp 0.3s ease;
        }
        
        .success-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .back-to-login {
            text-align: center;
            margin-top: 2rem;
        }
        
        .back-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header Section -->
            <div class="login-header">
                <div class="logo-section">
                    <img src="logo.jpg" alt="شعار النظام" class="logo-image" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="logo-icon" style="display: none;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </div>
                <div class="title-section">
                    <h1 class="system-title">نظام إدارة أمن المعلومات</h1>
                    <p class="system-subtitle">Information Security Management System</p>
                </div>
            </div>

            <!-- Reset Password Section -->
            <div class="login-form-section">
                <div class="reset-container">
                    <div class="reset-header">
                        <i class="fas fa-key reset-icon"></i>
                        <h2 class="reset-title">استعادة كلمة المرور</h2>
                        <p class="reset-subtitle">اختر طريقة استعادة كلمة المرور المناسبة لك</p>
                    </div>

                    <!-- Success Message -->
                    <div class="success-message" id="successMessage">
                        <i class="fas fa-check-circle success-icon"></i>
                        <h3>تم إرسال رمز الاستعادة بنجاح!</h3>
                        <p>تحقق من بريدك الإلكتروني أو رسائلك النصية للحصول على رمز الاستعادة</p>
                    </div>

                    <!-- Reset Methods -->
                    <div class="reset-methods">
                        <div class="reset-method" data-method="email">
                            <i class="fas fa-envelope method-icon"></i>
                            <div class="method-title">البريد الإلكتروني</div>
                            <div class="method-description">سنرسل رمز الاستعادة إلى بريدك الإلكتروني المسجل</div>
                        </div>
                        
                        <div class="reset-method" data-method="phone">
                            <i class="fas fa-mobile-alt method-icon"></i>
                            <div class="method-title">رسالة نصية</div>
                            <div class="method-description">سنرسل رمز الاستعادة إلى رقم هاتفك المسجل</div>
                        </div>
                        
                        <div class="reset-method" data-method="security">
                            <i class="fas fa-question-circle method-icon"></i>
                            <div class="method-title">الأسئلة الأمنية</div>
                            <div class="method-description">أجب على الأسئلة الأمنية التي قمت بتعيينها مسبقاً</div>
                        </div>
                        
                        <div class="reset-method" data-method="admin">
                            <i class="fas fa-user-shield method-icon"></i>
                            <div class="method-title">طلب من المدير</div>
                            <div class="method-description">اطلب من مدير النظام إعادة تعيين كلمة المرور</div>
                        </div>
                    </div>

                    <!-- Email Reset Form -->
                    <div class="reset-form" id="emailForm">
                        <h3><i class="fas fa-envelope"></i> استعادة عبر البريد الإلكتروني</h3>
                        <form id="emailResetForm">
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني:</label>
                                <input type="email" id="email" name="email" required 
                                       placeholder="أدخل بريدك الإلكتروني">
                            </div>
                            <button type="submit" class="login-btn">
                                <span class="btn-text">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال رمز الاستعادة
                                </span>
                            </button>
                        </form>
                    </div>

                    <!-- Phone Reset Form -->
                    <div class="reset-form" id="phoneForm">
                        <h3><i class="fas fa-mobile-alt"></i> استعادة عبر الرسائل النصية</h3>
                        <form id="phoneResetForm">
                            <div class="form-group">
                                <label for="phone">رقم الهاتف:</label>
                                <input type="tel" id="phone" name="phone" required 
                                       placeholder="أدخل رقم هاتفك">
                            </div>
                            <button type="submit" class="login-btn">
                                <span class="btn-text">
                                    <i class="fas fa-sms"></i>
                                    إرسال رمز الاستعادة
                                </span>
                            </button>
                        </form>
                    </div>

                    <!-- Security Questions Form -->
                    <div class="reset-form" id="securityForm">
                        <h3><i class="fas fa-question-circle"></i> الأسئلة الأمنية</h3>
                        <form id="securityResetForm">
                            <div class="form-group">
                                <label>ما هو اسم مدينتك الأولى؟</label>
                                <input type="text" name="question1" required 
                                       placeholder="أدخل إجابتك">
                            </div>
                            <div class="form-group">
                                <label>ما هو اسم حيوانك الأليف المفضل؟</label>
                                <input type="text" name="question2" required 
                                       placeholder="أدخل إجابتك">
                            </div>
                            <button type="submit" class="login-btn">
                                <span class="btn-text">
                                    <i class="fas fa-check"></i>
                                    التحقق من الإجابات
                                </span>
                            </button>
                        </form>
                    </div>

                    <!-- Admin Request Form -->
                    <div class="reset-form" id="adminForm">
                        <h3><i class="fas fa-user-shield"></i> طلب من المدير</h3>
                        <div style="text-align: center; padding: 2rem;">
                            <i class="fas fa-phone" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                            <p><strong>للحصول على مساعدة من المدير:</strong></p>
                            <p>📞 اتصل على: <strong>+966-XX-XXX-XXXX</strong></p>
                            <p>📧 أرسل إيميل إلى: <strong><EMAIL></strong></p>
                            <p>🏢 أو توجه إلى مكتب تقنية المعلومات</p>
                        </div>
                    </div>

                    <!-- Back to Login -->
                    <div class="back-to-login">
                        <a href="login.html" class="back-link">
                            <i class="fas fa-arrow-right"></i>
                            العودة إلى تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="login-footer">
                <p>&copy; 2024 نظام إدارة أمن المعلومات. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>

    <script src="forgot-password.js"></script>
</body>
</html>
