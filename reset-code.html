<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدخال رمز الاستعادة - نظام إدارة أمن المعلومات</title>
    <link rel="stylesheet" href="login-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .code-container {
            max-width: 450px;
            margin: 0 auto;
            text-align: center;
        }
        
        .code-header {
            margin-bottom: 2rem;
        }
        
        .code-icon {
            font-size: 3rem;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }
        
        .code-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .code-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.5;
        }
        
        .code-input-container {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin: 2rem 0;
        }
        
        .code-digit {
            width: 50px;
            height: 60px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--text-primary);
            background: white;
            transition: all 0.3s ease;
        }
        
        .code-digit:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: scale(1.05);
        }
        
        .code-digit.filled {
            border-color: var(--primary-blue);
            background: rgba(59, 130, 246, 0.05);
        }
        
        .timer-container {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .timer-text {
            color: #dc2626;
            font-weight: 600;
        }
        
        .timer-expired {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.4);
        }
        
        .resend-container {
            margin: 1.5rem 0;
        }
        
        .resend-btn {
            background: none;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .resend-btn:hover:not(:disabled) {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-1px);
        }
        
        .resend-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .verification-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            display: none;
        }
        
        .verification-success.show {
            display: block;
            animation: fadeInUp 0.3s ease;
        }
        
        .success-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header Section -->
            <div class="login-header">
                <div class="logo-section">
                    <img src="logo.jpg" alt="شعار النظام" class="logo-image" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="logo-icon" style="display: none;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </div>
                <div class="title-section">
                    <h1 class="system-title">نظام إدارة أمن المعلومات</h1>
                    <p class="system-subtitle">Information Security Management System</p>
                </div>
            </div>

            <!-- Code Verification Section -->
            <div class="login-form-section">
                <div class="code-container">
                    <div class="code-header">
                        <i class="fas fa-mobile-alt code-icon"></i>
                        <h2 class="code-title">إدخال رمز التحقق</h2>
                        <p class="code-subtitle">أدخل الرمز المكون من 6 أرقام الذي تم إرساله إليك</p>
                    </div>

                    <!-- Success Message -->
                    <div class="verification-success" id="verificationSuccess">
                        <i class="fas fa-check-circle success-icon"></i>
                        <h3>تم التحقق بنجاح!</h3>
                        <p>سيتم توجيهك لتعيين كلمة مرور جديدة</p>
                    </div>

                    <!-- Code Input -->
                    <form id="codeVerificationForm">
                        <div class="code-input-container">
                            <input type="text" class="code-digit" maxlength="1" data-index="0">
                            <input type="text" class="code-digit" maxlength="1" data-index="1">
                            <input type="text" class="code-digit" maxlength="1" data-index="2">
                            <input type="text" class="code-digit" maxlength="1" data-index="3">
                            <input type="text" class="code-digit" maxlength="1" data-index="4">
                            <input type="text" class="code-digit" maxlength="1" data-index="5">
                        </div>

                        <!-- Timer -->
                        <div class="timer-container" id="timerContainer">
                            <div class="timer-text">
                                <i class="fas fa-clock"></i>
                                الرمز صالح لمدة: <span id="timerDisplay">15:00</span>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="login-btn" id="verifyBtn">
                            <span class="btn-text">
                                <i class="fas fa-check"></i>
                                تحقق من الرمز
                            </span>
                            <div class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                                جاري التحقق...
                            </div>
                        </button>

                        <!-- Resend Code -->
                        <div class="resend-container">
                            <p>لم تستلم الرمز؟</p>
                            <button type="button" class="resend-btn" id="resendBtn" disabled>
                                <i class="fas fa-redo"></i>
                                إعادة إرسال الرمز (<span id="resendTimer">60</span>)
                            </button>
                        </div>
                    </form>

                    <!-- Error Message -->
                    <div class="error-message" id="codeError" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="codeErrorMessage">رمز غير صحيح</span>
                    </div>

                    <!-- Back to Login -->
                    <div class="back-to-login" style="margin-top: 2rem;">
                        <a href="login.html" class="back-link">
                            <i class="fas fa-arrow-right"></i>
                            العودة إلى تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="login-footer">
                <p>&copy; 2024 نظام إدارة أمن المعلومات. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>

    <script>
        // Code verification functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeCodeVerification();
        });

        function initializeCodeVerification() {
            const codeInputs = document.querySelectorAll('.code-digit');
            const form = document.getElementById('codeVerificationForm');
            const verifyBtn = document.getElementById('verifyBtn');
            const resendBtn = document.getElementById('resendBtn');
            
            // Setup code input behavior
            setupCodeInputs(codeInputs);
            
            // Setup form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                verifyCode();
            });
            
            // Setup resend button
            resendBtn.addEventListener('click', resendCode);
            
            // Start timers
            startCodeTimer();
            startResendTimer();
            
            // Check if we have reset data
            checkResetData();
        }

        function setupCodeInputs(inputs) {
            inputs.forEach((input, index) => {
                input.addEventListener('input', function(e) {
                    const value = e.target.value;
                    
                    // Only allow numbers
                    if (!/^\d$/.test(value)) {
                        e.target.value = '';
                        return;
                    }
                    
                    // Add filled class
                    e.target.classList.add('filled');
                    
                    // Move to next input
                    if (value && index < inputs.length - 1) {
                        inputs[index + 1].focus();
                    }
                    
                    // Check if all inputs are filled
                    checkAllInputsFilled();
                });
                
                input.addEventListener('keydown', function(e) {
                    // Handle backspace
                    if (e.key === 'Backspace' && !e.target.value && index > 0) {
                        inputs[index - 1].focus();
                        inputs[index - 1].classList.remove('filled');
                    }
                });
                
                input.addEventListener('paste', function(e) {
                    e.preventDefault();
                    const pastedData = e.clipboardData.getData('text');
                    const digits = pastedData.replace(/\D/g, '').slice(0, 6);
                    
                    digits.split('').forEach((digit, i) => {
                        if (inputs[i]) {
                            inputs[i].value = digit;
                            inputs[i].classList.add('filled');
                        }
                    });
                    
                    checkAllInputsFilled();
                });
            });
        }

        function checkAllInputsFilled() {
            const inputs = document.querySelectorAll('.code-digit');
            const allFilled = Array.from(inputs).every(input => input.value);
            
            const verifyBtn = document.getElementById('verifyBtn');
            verifyBtn.disabled = !allFilled;
            
            if (allFilled) {
                verifyBtn.style.opacity = '1';
                verifyBtn.style.transform = 'scale(1.02)';
            } else {
                verifyBtn.style.opacity = '0.7';
                verifyBtn.style.transform = 'scale(1)';
            }
        }

        function verifyCode() {
            const inputs = document.querySelectorAll('.code-digit');
            const code = Array.from(inputs).map(input => input.value).join('');
            
            if (code.length !== 6) {
                showError('يرجى إدخال الرمز كاملاً');
                return;
            }
            
            // Show loading
            showLoading(true);
            
            // Simulate verification
            setTimeout(() => {
                const savedResetData = JSON.parse(localStorage.getItem('resetCode') || '{}');
                
                if (savedResetData.code && savedResetData.code.toString() === code) {
                    // Check if code is expired
                    if (Date.now() > savedResetData.expires) {
                        showError('انتهت صلاحية الرمز. يرجى طلب رمز جديد');
                        showLoading(false);
                        return;
                    }
                    
                    // Code is correct
                    showSuccess();
                    
                    // Redirect to new password page
                    setTimeout(() => {
                        window.location.href = 'new-password.html';
                    }, 2000);
                } else {
                    showError('الرمز غير صحيح. يرجى المحاولة مرة أخرى');
                    showLoading(false);
                    clearInputs();
                }
            }, 2000);
        }

        function showSuccess() {
            const successDiv = document.getElementById('verificationSuccess');
            successDiv.classList.add('show');
            
            // Hide form
            document.getElementById('codeVerificationForm').style.display = 'none';
        }

        function clearInputs() {
            const inputs = document.querySelectorAll('.code-digit');
            inputs.forEach(input => {
                input.value = '';
                input.classList.remove('filled');
            });
            inputs[0].focus();
        }

        function resendCode() {
            // Simulate resending code
            console.log('إعادة إرسال الرمز...');
            
            // Generate new code
            const newCode = Math.floor(100000 + Math.random() * 900000);
            const resetData = JSON.parse(localStorage.getItem('resetCode') || '{}');
            resetData.code = newCode;
            resetData.expires = Date.now() + (15 * 60 * 1000);
            localStorage.setItem('resetCode', JSON.stringify(resetData));
            
            console.log(`الرمز الجديد: ${newCode}`);
            
            // Restart timers
            startCodeTimer();
            startResendTimer();
            
            showMessage('تم إرسال رمز جديد');
        }

        function startCodeTimer() {
            let timeLeft = 15 * 60; // 15 minutes
            const timerDisplay = document.getElementById('timerDisplay');
            const timerContainer = document.getElementById('timerContainer');
            
            const timer = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                if (timeLeft <= 0) {
                    clearInterval(timer);
                    timerContainer.classList.add('timer-expired');
                    timerDisplay.textContent = 'انتهت الصلاحية';
                    document.getElementById('verifyBtn').disabled = true;
                }
                
                timeLeft--;
            }, 1000);
        }

        function startResendTimer() {
            let timeLeft = 60;
            const resendBtn = document.getElementById('resendBtn');
            const resendTimer = document.getElementById('resendTimer');
            
            resendBtn.disabled = true;
            
            const timer = setInterval(() => {
                resendTimer.textContent = timeLeft;
                
                if (timeLeft <= 0) {
                    clearInterval(timer);
                    resendBtn.disabled = false;
                    resendBtn.innerHTML = '<i class="fas fa-redo"></i> إعادة إرسال الرمز';
                }
                
                timeLeft--;
            }, 1000);
        }

        function checkResetData() {
            const resetData = JSON.parse(localStorage.getItem('resetCode') || '{}');
            if (!resetData.code) {
                // No reset data, redirect to forgot password
                window.location.href = 'forgot-password.html';
            }
        }

        function showLoading(show) {
            const verifyBtn = document.getElementById('verifyBtn');
            const btnText = verifyBtn.querySelector('.btn-text');
            const btnLoading = verifyBtn.querySelector('.btn-loading');
            
            if (show) {
                verifyBtn.disabled = true;
                btnText.style.display = 'none';
                btnLoading.style.display = 'block';
            } else {
                verifyBtn.disabled = false;
                btnText.style.display = 'block';
                btnLoading.style.display = 'none';
            }
        }

        function showError(message) {
            const errorDiv = document.getElementById('codeError');
            const errorMessage = document.getElementById('codeErrorMessage');
            
            errorMessage.textContent = message;
            errorDiv.style.display = 'flex';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function showMessage(message) {
            // Create temporary message
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                background: #10b981;
                color: white;
                padding: 1rem;
                border-radius: 8px;
                text-align: center;
                margin: 1rem 0;
                animation: fadeInUp 0.3s ease;
            `;
            messageDiv.innerHTML = `<i class="fas fa-check"></i> ${message}`;
            
            document.querySelector('.code-container').insertBefore(
                messageDiv, 
                document.getElementById('codeVerificationForm')
            );
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
    </script>
</body>
</html>
